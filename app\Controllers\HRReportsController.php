<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\BranchesModel;

use CodeIgniter\Controller;

/**
 * HRReportsController
 * 
 * Handles the reporting functionality for Human Resources analytics.
 * Displays visual reports, charts, and data tables for HR management including
 * gender distribution, demographics, employment timeline, and tenure analysis.
 * 
 * @package App\Controllers
 */
class HRReportsController extends Controller
{
    protected $userModel;
    protected $branchesModel;

    /**
     * Constructor initializes models
     */
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->branchesModel = new BranchesModel();
    }

    /**
     * Display the HR Reports Dashboard (Read-only)
     */
    public function index()
    {
        // Get all users with branch information
        $users = $this->userModel->getAllUsersWithBranches();
        
        // Get all branches
        $branches = $this->branchesModel->findAll();
        
        // Prepare summary statistics
        $summaryStats = $this->prepareSummaryStats($users);
        
        // Prepare data for overview charts
        $chartData = $this->prepareOverviewChartData($users, $branches);

        // Pass all data to the view
        return view('reports_hr/reports_hr_index', [
            'title' => 'HR Reports Dashboard',
            'users' => $users,
            'branches' => $branches,
            'summaryStats' => $summaryStats,
            'chartData' => $chartData,
        ]);
    }

    /**
     * Display Gender Analytics Report
     */
    public function gender()
    {
        // Get all users with branch information
        $users = $this->userModel->getAllUsersWithBranches();
        
        // Get all branches
        $branches = $this->branchesModel->findAll();
        
        // Prepare gender analytics data
        $genderData = $this->prepareGenderAnalytics($users, $branches);
        
        // Prepare data for gender charts
        $chartData = $this->prepareGenderChartData($users, $branches);

        // Pass all data to the view
        return view('reports_hr/reports_hr_gender', [
            'title' => 'Gender Analytics Report',
            'users' => $users,
            'branches' => $branches,
            'genderData' => $genderData,
            'chartData' => $chartData,
        ]);
    }

    /**
     * Display Date Joined Analytics Report
     */
    public function dateJoined()
    {
        // Get all users with branch information
        $users = $this->userModel->getAllUsersWithBranches();
        
        // Get all branches
        $branches = $this->branchesModel->findAll();
        
        // Prepare date joined analytics data
        $dateJoinedData = $this->prepareDateJoinedAnalytics($users, $branches);
        
        // Prepare data for date joined charts
        $chartData = $this->prepareDateJoinedChartData($users, $branches);

        // Pass all data to the view
        return view('reports_hr/reports_hr_date_joined', [
            'title' => 'Employment Timeline & Tenure Analysis',
            'users' => $users,
            'branches' => $branches,
            'dateJoinedData' => $dateJoinedData,
            'chartData' => $chartData,
        ]);
    }



    /**
     * Prepare summary statistics for HR dashboard
     *
     * @param array $users
     * @return array
     */
    private function prepareSummaryStats($users)
    {
        $totalUsers = count($users);
        $maleCount = 0;
        $femaleCount = 0;
        $evaluatorCount = 0;
        $totalTenure = 0;
        $usersWithJoinDate = 0;

        foreach ($users as $user) {
            // Gender count
            if (strtolower($user['gender']) === 'male') {
                $maleCount++;
            } elseif (strtolower($user['gender']) === 'female') {
                $femaleCount++;
            }

            // Evaluator count
            if ($user['is_evaluator'] == 1) {
                $evaluatorCount++;
            }

            // Tenure calculation
            if (!empty($user['joined_date'])) {
                $joinDate = new \DateTime($user['joined_date']);
                $currentDate = new \DateTime();
                $tenure = $currentDate->diff($joinDate)->days / 365.25; // Convert to years
                $totalTenure += $tenure;
                $usersWithJoinDate++;
            }
        }

        $averageTenure = $usersWithJoinDate > 0 ? $totalTenure / $usersWithJoinDate : 0;

        return [
            'total_users' => $totalUsers,
            'male_count' => $maleCount,
            'female_count' => $femaleCount,
            'male_percentage' => $totalUsers > 0 ? round(($maleCount / $totalUsers) * 100, 1) : 0,
            'female_percentage' => $totalUsers > 0 ? round(($femaleCount / $totalUsers) * 100, 1) : 0,
            'evaluator_count' => $evaluatorCount,
            'evaluator_percentage' => $totalUsers > 0 ? round(($evaluatorCount / $totalUsers) * 100, 1) : 0,
            'average_tenure' => round($averageTenure, 1),
            'users_with_join_date' => $usersWithJoinDate,
        ];
    }

    /**
     * Prepare overview chart data
     *
     * @param array $users
     * @param array $branches
     * @return array
     */
    private function prepareOverviewChartData($users, $branches)
    {
        // Gender distribution
        $genderData = ['male' => 0, 'female' => 0, 'unspecified' => 0];
        
        // Role distribution
        $roleData = [];
        
        // Branch distribution
        $branchData = [];
        
        foreach ($users as $user) {
            // Gender distribution
            $gender = strtolower($user['gender'] ?? '');
            if ($gender === 'male') {
                $genderData['male']++;
            } elseif ($gender === 'female') {
                $genderData['female']++;
            } else {
                $genderData['unspecified']++;
            }

            // Role distribution
            $role = $user['role'] ?? 'unspecified';
            $roleData[$role] = ($roleData[$role] ?? 0) + 1;

            // Branch distribution
            $branchName = $user['branch_name'] ?? 'Unassigned';
            $branchData[$branchName] = ($branchData[$branchName] ?? 0) + 1;
        }

        return [
            'gender' => $genderData,
            'roles' => $roleData,
            'branches' => $branchData,
        ];
    }

    /**
     * Prepare gender analytics data
     *
     * @param array $users
     * @param array $branches
     * @return array
     */
    private function prepareGenderAnalytics($users, $branches)
    {
        $analytics = [
            'total_users' => count($users),
            'gender_summary' => ['male' => 0, 'female' => 0, 'unspecified' => 0],
            'gender_by_role' => [],
            'gender_by_branch' => [],
            'gender_by_grade' => [],
            'evaluator_by_gender' => ['male' => 0, 'female' => 0, 'unspecified' => 0],
        ];

        foreach ($users as $user) {
            $gender = strtolower($user['gender'] ?? '');
            $genderKey = in_array($gender, ['male', 'female']) ? $gender : 'unspecified';

            // Gender summary
            $analytics['gender_summary'][$genderKey]++;

            // Gender by role
            $role = $user['role'] ?? 'unspecified';
            if (!isset($analytics['gender_by_role'][$role])) {
                $analytics['gender_by_role'][$role] = ['male' => 0, 'female' => 0, 'unspecified' => 0];
            }
            $analytics['gender_by_role'][$role][$genderKey]++;

            // Gender by branch
            $branchName = $user['branch_name'] ?? 'Unassigned';
            if (!isset($analytics['gender_by_branch'][$branchName])) {
                $analytics['gender_by_branch'][$branchName] = ['male' => 0, 'female' => 0, 'unspecified' => 0];
            }
            $analytics['gender_by_branch'][$branchName][$genderKey]++;

            // Gender by grade
            $grade = $user['grade'] ?? 'Unspecified';
            if (!isset($analytics['gender_by_grade'][$grade])) {
                $analytics['gender_by_grade'][$grade] = ['male' => 0, 'female' => 0, 'unspecified' => 0];
            }
            $analytics['gender_by_grade'][$grade][$genderKey]++;

            // Evaluator by gender
            if ($user['is_evaluator'] == 1) {
                $analytics['evaluator_by_gender'][$genderKey]++;
            }
        }

        // Calculate percentages
        $total = $analytics['total_users'];
        $analytics['gender_percentages'] = [
            'male' => $total > 0 ? round(($analytics['gender_summary']['male'] / $total) * 100, 1) : 0,
            'female' => $total > 0 ? round(($analytics['gender_summary']['female'] / $total) * 100, 1) : 0,
            'unspecified' => $total > 0 ? round(($analytics['gender_summary']['unspecified'] / $total) * 100, 1) : 0,
        ];

        return $analytics;
    }

    /**
     * Prepare gender chart data
     *
     * @param array $users
     * @param array $branches
     * @return array
     */
    private function prepareGenderChartData($users, $branches)
    {
        $analytics = $this->prepareGenderAnalytics($users, $branches);

        return [
            'gender_summary' => $analytics['gender_summary'],
            'gender_by_role' => $analytics['gender_by_role'],
            'gender_by_branch' => $analytics['gender_by_branch'],
            'gender_by_grade' => $analytics['gender_by_grade'],
            'evaluator_by_gender' => $analytics['evaluator_by_gender'],
        ];
    }

    /**
     * Prepare date joined analytics data
     *
     * @param array $users
     * @param array $branches
     * @return array
     */
    private function prepareDateJoinedAnalytics($users, $branches)
    {
        $analytics = [
            'total_users' => count($users),
            'users_with_join_date' => 0,
            'hiring_by_year' => [],
            'hiring_by_month' => [],
            'tenure_distribution' => [],
            'average_tenure' => 0,
            'tenure_by_role' => [],
            'tenure_by_branch' => [],
            'recent_hires' => [],
            'long_tenure' => [],
        ];

        $totalTenure = 0;
        $usersWithJoinDate = 0;
        $currentDate = new \DateTime();

        foreach ($users as $user) {
            if (!empty($user['joined_date'])) {
                $usersWithJoinDate++;
                $joinDate = new \DateTime($user['joined_date']);

                // Hiring by year
                $year = $joinDate->format('Y');
                $analytics['hiring_by_year'][$year] = ($analytics['hiring_by_year'][$year] ?? 0) + 1;

                // Hiring by month (for current year and last year)
                $monthKey = $joinDate->format('Y-m');
                $analytics['hiring_by_month'][$monthKey] = ($analytics['hiring_by_month'][$monthKey] ?? 0) + 1;

                // Calculate tenure
                $tenure = $currentDate->diff($joinDate);
                $tenureYears = $tenure->y + ($tenure->m / 12) + ($tenure->d / 365);
                $totalTenure += $tenureYears;

                // Tenure distribution
                $tenureRange = $this->getTenureRange($tenureYears);
                $analytics['tenure_distribution'][$tenureRange] = ($analytics['tenure_distribution'][$tenureRange] ?? 0) + 1;

                // Tenure by role
                $role = $user['role'] ?? 'unspecified';
                if (!isset($analytics['tenure_by_role'][$role])) {
                    $analytics['tenure_by_role'][$role] = ['total_tenure' => 0, 'count' => 0];
                }
                $analytics['tenure_by_role'][$role]['total_tenure'] += $tenureYears;
                $analytics['tenure_by_role'][$role]['count']++;

                // Tenure by branch
                $branchName = $user['branch_name'] ?? 'Unassigned';
                if (!isset($analytics['tenure_by_branch'][$branchName])) {
                    $analytics['tenure_by_branch'][$branchName] = ['total_tenure' => 0, 'count' => 0];
                }
                $analytics['tenure_by_branch'][$branchName]['total_tenure'] += $tenureYears;
                $analytics['tenure_by_branch'][$branchName]['count']++;

                // Recent hires (less than 1 year)
                if ($tenureYears < 1) {
                    $analytics['recent_hires'][] = array_merge($user, ['tenure_years' => $tenureYears]);
                }

                // Long tenure (more than 10 years)
                if ($tenureYears > 10) {
                    $analytics['long_tenure'][] = array_merge($user, ['tenure_years' => $tenureYears]);
                }
            }
        }

        $analytics['users_with_join_date'] = $usersWithJoinDate;
        $analytics['average_tenure'] = $usersWithJoinDate > 0 ? $totalTenure / $usersWithJoinDate : 0;

        // Calculate average tenure by role
        foreach ($analytics['tenure_by_role'] as $role => &$data) {
            $data['average_tenure'] = $data['count'] > 0 ? $data['total_tenure'] / $data['count'] : 0;
        }

        // Calculate average tenure by branch
        foreach ($analytics['tenure_by_branch'] as $branch => &$data) {
            $data['average_tenure'] = $data['count'] > 0 ? $data['total_tenure'] / $data['count'] : 0;
        }

        // Sort arrays
        ksort($analytics['hiring_by_year']);
        ksort($analytics['hiring_by_month']);

        return $analytics;
    }

    /**
     * Get tenure range for grouping
     *
     * @param float $tenureYears
     * @return string
     */
    private function getTenureRange($tenureYears)
    {
        if ($tenureYears < 1) return '0-1 years';
        if ($tenureYears < 3) return '1-3 years';
        if ($tenureYears < 5) return '3-5 years';
        if ($tenureYears < 10) return '5-10 years';
        if ($tenureYears < 15) return '10-15 years';
        return '15+ years';
    }

    /**
     * Prepare date joined chart data
     *
     * @param array $users
     * @param array $branches
     * @return array
     */
    private function prepareDateJoinedChartData($users, $branches)
    {
        $analytics = $this->prepareDateJoinedAnalytics($users, $branches);

        return [
            'hiring_by_year' => $analytics['hiring_by_year'],
            'hiring_by_month' => $analytics['hiring_by_month'],
            'tenure_distribution' => $analytics['tenure_distribution'],
            'tenure_by_role' => $analytics['tenure_by_role'],
            'tenure_by_branch' => $analytics['tenure_by_branch'],
        ];
    }
}
