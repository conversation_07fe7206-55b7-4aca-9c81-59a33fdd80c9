<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title">
                                <i class="fas fa-users"></i> HR Reports Dashboard
                            </h4>
                            <p class="card-text mb-0">Comprehensive human resources analytics including gender distribution, demographics, employment timeline, and tenure analysis.</p>
                        </div>
                        <div>
                            <!-- PDF export functionality will be implemented later -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Employees</h6>
                            <h3><?= $summaryStats['total_users'] ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Male Employees</h6>
                            <h3><?= $summaryStats['male_count'] ?> <small>(<?= $summaryStats['male_percentage'] ?>%)</small></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-male fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Female Employees</h6>
                            <h3><?= $summaryStats['female_count'] ?> <small>(<?= $summaryStats['female_percentage'] ?>%)</small></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-female fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Average Tenure</h6>
                            <h3><?= $summaryStats['average_tenure'] ?> <small>years</small></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Navigation Cards -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-venus-mars fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Gender Analytics</h5>
                    <p class="card-text">Detailed gender distribution analysis across roles, branches, and grades.</p>
                    <a href="<?= base_url('reports/hr/gender') ?>" class="btn btn-primary">
                        <i class="fas fa-chart-pie me-1"></i> View Gender Analytics
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Employment Timeline</h5>
                    <p class="card-text">Hiring patterns, tenure analysis, and employment trends over time.</p>
                    <a href="<?= base_url('reports/hr/date-joined') ?>" class="btn btn-success">
                        <i class="fas fa-chart-line me-1"></i> View Timeline Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Charts -->
    <div class="row mb-4">
        <!-- Gender Distribution Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Gender Distribution</strong></div>
                <div class="card-body">
                    <canvas id="genderChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Role Distribution Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Role Distribution</strong></div>
                <div class="card-body">
                    <canvas id="roleChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Distribution Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Employee Distribution by Branch</strong></div>
                <div class="card-body">
                    <canvas id="branchChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>M&E Evaluators</strong></div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-primary"><?= $summaryStats['evaluator_count'] ?></h4>
                            <p class="mb-0">Total Evaluators</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?= $summaryStats['evaluator_percentage'] ?>%</h4>
                            <p class="mb-0">of Total Staff</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Employment Data Coverage</strong></div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-info"><?= $summaryStats['users_with_join_date'] ?></h4>
                            <p class="mb-0">With Join Date</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning"><?= $summaryStats['total_users'] - $summaryStats['users_with_join_date'] ?></h4>
                            <p class="mb-0">Missing Join Date</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Summary Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <strong>Employee Summary</strong>
                    <span class="badge bg-primary ms-2"><?= count($users) ?> Total</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>Employee</th>
                                    <th>Gender</th>
                                    <th>Role</th>
                                    <th>Branch</th>
                                    <th>Designation</th>
                                    <th>Grade</th>
                                    <th>Joined Date</th>
                                    <th>Tenure</th>
                                    <th>M&E Evaluator</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?= $counter++ ?></td>
                                    <td>
                                        <strong><?= esc($user['fname'] . ' ' . $user['lname']) ?></strong><br>
                                        <small class="text-muted"><?= esc($user['email']) ?></small>
                                    </td>
                                    <td>
                                        <?php if (strtolower($user['gender']) === 'male'): ?>
                                            <span class="badge bg-primary">Male</span>
                                        <?php elseif (strtolower($user['gender']) === 'female'): ?>
                                            <span class="badge bg-warning">Female</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Not Specified</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><span class="badge bg-info"><?= esc(ucfirst($user['role'])) ?></span></td>
                                    <td><?= esc($user['branch_name'] ?? 'Unassigned') ?></td>
                                    <td><?= esc($user['designation'] ?? 'N/A') ?></td>
                                    <td><?= esc($user['grade'] ?? 'N/A') ?></td>
                                    <td>
                                        <?php if (!empty($user['joined_date'])): ?>
                                            <?= date('M d, Y', strtotime($user['joined_date'])) ?>
                                        <?php else: ?>
                                            <span class="text-muted">Not specified</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($user['joined_date'])): ?>
                                            <?php
                                            $joinDate = new DateTime($user['joined_date']);
                                            $currentDate = new DateTime();
                                            $tenure = $currentDate->diff($joinDate);
                                            $years = $tenure->y;
                                            $months = $tenure->m;
                                            ?>
                                            <?= $years ?> years, <?= $months ?> months
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['is_evaluator'] == 1): ?>
                                            <span class="badge bg-success">Yes</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Chart.js configuration for HR Reports
document.addEventListener('DOMContentLoaded', function() {
    // Gender Distribution Pie Chart
    const genderCtx = document.getElementById('genderChart').getContext('2d');
    new Chart(genderCtx, {
        type: 'pie',
        data: {
            labels: ['Male', 'Female', 'Unspecified'],
            datasets: [{
                data: [
                    <?= $chartData['gender']['male'] ?>,
                    <?= $chartData['gender']['female'] ?>,
                    <?= $chartData['gender']['unspecified'] ?>
                ],
                backgroundColor: ['#007bff', '#ffc107', '#6c757d'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Role Distribution Bar Chart
    const roleCtx = document.getElementById('roleChart').getContext('2d');
    new Chart(roleCtx, {
        type: 'bar',
        data: {
            labels: <?= json_encode(array_keys($chartData['roles'])) ?>,
            datasets: [{
                label: 'Number of Employees',
                data: <?= json_encode(array_values($chartData['roles'])) ?>,
                backgroundColor: '#28a745',
                borderColor: '#1e7e34',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Branch Distribution Bar Chart
    const branchCtx = document.getElementById('branchChart').getContext('2d');
    new Chart(branchCtx, {
        type: 'bar',
        data: {
            labels: <?= json_encode(array_keys($chartData['branches'])) ?>,
            datasets: [{
                label: 'Number of Employees',
                data: <?= json_encode(array_values($chartData['branches'])) ?>,
                backgroundColor: '#17a2b8',
                borderColor: '#117a8b',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>
