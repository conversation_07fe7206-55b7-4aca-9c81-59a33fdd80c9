<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title">
                                <i class="fas fa-calendar-check"></i> Employment Timeline & Tenure Analysis
                            </h4>
                            <p class="card-text mb-0">Comprehensive analysis of hiring patterns, employee tenure, retention rates, and employment trends over time.</p>
                        </div>
                        <div>
                            <a href="<?= base_url('reports/hr') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-left me-1"></i> Back to HR Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Employees</h6>
                            <h3><?= $dateJoinedData['total_users'] ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">With Join Date</h6>
                            <h3><?= $dateJoinedData['users_with_join_date'] ?></h3>
                            <p class="mb-0"><?= round(($dateJoinedData['users_with_join_date'] / $dateJoinedData['total_users']) * 100, 1) ?>% coverage</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Average Tenure</h6>
                            <h3><?= round($dateJoinedData['average_tenure'], 1) ?> <small>years</small></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Recent Hires</h6>
                            <h3><?= count($dateJoinedData['recent_hires']) ?></h3>
                            <p class="mb-0">< 1 year tenure</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hiring Trends Charts -->
    <div class="row mb-4">
        <!-- Hiring by Year -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Hiring Trends by Year</strong></div>
                <div class="card-body">
                    <canvas id="hiringByYearChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Tenure Distribution -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Tenure Distribution</strong></div>
                <div class="card-body">
                    <canvas id="tenureDistributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Hiring Trends -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Monthly Hiring Trends (Last 24 Months)</strong></div>
                <div class="card-body">
                    <canvas id="monthlyHiringChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Tenure Analysis by Role and Branch -->
    <div class="row mb-4">
        <!-- Average Tenure by Role -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Average Tenure by Role</strong></div>
                <div class="card-body">
                    <canvas id="tenureByRoleChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Average Tenure by Branch -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Average Tenure by Branch</strong></div>
                <div class="card-body">
                    <canvas id="tenureByBranchChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row mb-4">
        <!-- Tenure by Role Table -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Tenure Analysis by Role</strong></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Role</th>
                                    <th>Count</th>
                                    <th>Avg Tenure</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dateJoinedData['tenure_by_role'] as $role => $data): ?>
                                <tr>
                                    <td><strong><?= esc(ucfirst($role)) ?></strong></td>
                                    <td><span class="badge bg-primary"><?= $data['count'] ?></span></td>
                                    <td><?= round($data['average_tenure'], 1) ?> years</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tenure by Branch Table -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Tenure Analysis by Branch</strong></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Branch</th>
                                    <th>Count</th>
                                    <th>Avg Tenure</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dateJoinedData['tenure_by_branch'] as $branch => $data): ?>
                                <tr>
                                    <td><strong><?= esc($branch) ?></strong></td>
                                    <td><span class="badge bg-success"><?= $data['count'] ?></span></td>
                                    <td><?= round($data['average_tenure'], 1) ?> years</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Hires and Long Tenure Employees -->
    <div class="row mb-4">
        <!-- Recent Hires -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <strong>Recent Hires (< 1 Year)</strong>
                    <span class="badge bg-danger ms-2"><?= count($dateJoinedData['recent_hires']) ?></span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Employee</th>
                                    <th>Role</th>
                                    <th>Joined</th>
                                    <th>Tenure</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($dateJoinedData['recent_hires'], 0, 10) as $hire): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($hire['fname'] . ' ' . $hire['lname']) ?></strong><br>
                                        <small class="text-muted"><?= esc($hire['email']) ?></small>
                                    </td>
                                    <td><span class="badge bg-info"><?= esc(ucfirst($hire['role'])) ?></span></td>
                                    <td><?= date('M d, Y', strtotime($hire['joined_date'])) ?></td>
                                    <td><?= round($hire['tenure_years'] * 12) ?> months</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Long Tenure Employees -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <strong>Long Tenure Employees (10+ Years)</strong>
                    <span class="badge bg-warning ms-2"><?= count($dateJoinedData['long_tenure']) ?></span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Employee</th>
                                    <th>Role</th>
                                    <th>Joined</th>
                                    <th>Tenure</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                // Sort by tenure descending
                                usort($dateJoinedData['long_tenure'], function($a, $b) {
                                    return $b['tenure_years'] <=> $a['tenure_years'];
                                });
                                ?>
                                <?php foreach (array_slice($dateJoinedData['long_tenure'], 0, 10) as $employee): ?>
                                <tr>
                                    <td>
                                        <strong><?= esc($employee['fname'] . ' ' . $employee['lname']) ?></strong><br>
                                        <small class="text-muted"><?= esc($employee['email']) ?></small>
                                    </td>
                                    <td><span class="badge bg-success"><?= esc(ucfirst($employee['role'])) ?></span></td>
                                    <td><?= date('M d, Y', strtotime($employee['joined_date'])) ?></td>
                                    <td><?= round($employee['tenure_years'], 1) ?> years</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tenure Distribution Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Tenure Distribution Breakdown</strong></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Tenure Range</th>
                                    <th>Number of Employees</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $totalWithTenure = array_sum($dateJoinedData['tenure_distribution']);
                                foreach ($dateJoinedData['tenure_distribution'] as $range => $count): 
                                    $percentage = $totalWithTenure > 0 ? round(($count / $totalWithTenure) * 100, 1) : 0;
                                ?>
                                <tr>
                                    <td><strong><?= esc($range) ?></strong></td>
                                    <td><span class="badge bg-primary"><?= $count ?></span></td>
                                    <td><?= $percentage ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Chart.js configuration for Date Joined Analytics
document.addEventListener('DOMContentLoaded', function() {
    // Hiring by Year Line Chart
    const yearCtx = document.getElementById('hiringByYearChart').getContext('2d');
    new Chart(yearCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode(array_keys($chartData['hiring_by_year'])) ?>,
            datasets: [{
                label: 'New Hires',
                data: <?= json_encode(array_values($chartData['hiring_by_year'])) ?>,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Tenure Distribution Pie Chart
    const tenureCtx = document.getElementById('tenureDistributionChart').getContext('2d');
    new Chart(tenureCtx, {
        type: 'doughnut',
        data: {
            labels: <?= json_encode(array_keys($chartData['tenure_distribution'])) ?>,
            datasets: [{
                data: <?= json_encode(array_values($chartData['tenure_distribution'])) ?>,
                backgroundColor: [
                    '#ff6384', '#36a2eb', '#ffce56', '#4bc0c0', '#9966ff', '#ff9f40'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Hiring Trends
    const monthlyCtx = document.getElementById('monthlyHiringChart').getContext('2d');
    // Get last 24 months of data
    const monthlyData = <?= json_encode($chartData['hiring_by_month']) ?>;
    const sortedMonths = Object.keys(monthlyData).sort().slice(-24);
    const monthlyValues = sortedMonths.map(month => monthlyData[month] || 0);
    
    new Chart(monthlyCtx, {
        type: 'bar',
        data: {
            labels: sortedMonths.map(month => {
                const date = new Date(month + '-01');
                return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
            }),
            datasets: [{
                label: 'New Hires',
                data: monthlyValues,
                backgroundColor: '#28a745',
                borderColor: '#1e7e34',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Average Tenure by Role
    const roleLabels = <?= json_encode(array_keys($chartData['tenure_by_role'])) ?>;
    const roleData = roleLabels.map(role => <?= json_encode($chartData['tenure_by_role']) ?>[role].average_tenure);
    
    const roleCtx = document.getElementById('tenureByRoleChart').getContext('2d');
    new Chart(roleCtx, {
        type: 'bar',
        data: {
            labels: roleLabels,
            datasets: [{
                label: 'Average Tenure (Years)',
                data: roleData,
                backgroundColor: '#17a2b8',
                borderColor: '#117a8b',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Average Tenure by Branch
    const branchLabels = <?= json_encode(array_keys($chartData['tenure_by_branch'])) ?>;
    const branchData = branchLabels.map(branch => <?= json_encode($chartData['tenure_by_branch']) ?>[branch].average_tenure);
    
    const branchCtx = document.getElementById('tenureByBranchChart').getContext('2d');
    new Chart(branchCtx, {
        type: 'horizontalBar',
        data: {
            labels: branchLabels,
            datasets: [{
                label: 'Average Tenure (Years)',
                data: branchData,
                backgroundColor: '#ffc107',
                borderColor: '#e0a800',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>
